<div class="page-block">
    <div class="action-bar">
        <div class="ab-left">
            <h1>{{ isNew ? 'Create Decal' : 'Edit Decal' }}</h1>
        </div>
        <div class="ab-right">
            <button
                class="btn btn-primary"
                type="button"
                (click)="isNew ? create() : save()"
                [disabled]="detailForm.invalid || detailForm.pristine"
            >
                {{ isNew ? 'Create' : 'Update' }}
            </button>
        </div>
    </div>

    <form class="form" [formGroup]="detailForm">
        <div class="form-group">
            <label for="code">Code</label>
            <input
                id="code"
                type="text"
                class="form-control"
                formControlName="code"
                [readonly]="!isNew"
                (input)="detailForm.markAsDirty()"
            />
        </div>

        <div class="form-group">
            <label for="name">Name</label>
            <input
                id="name"
                type="text"
                class="form-control"
                formControlName="name"
                (input)="detailForm.markAsDirty()"
            />
        </div>

        <div class="form-group">
            <label for="description">Description</label>
            <textarea
                id="description"
                class="form-control"
                formControlName="description"
                rows="3"
                (input)="detailForm.markAsDirty()"
            ></textarea>
        </div>

        <div class="form-group">
            <label for="price">Price ($)</label>
            <input
                id="price"
                type="number"
                class="form-control"
                step="0.01"
                min="0"
                formControlName="price"
                (input)="detailForm.markAsDirty()"
            />
        </div>

        <div class="form-group">
            <label for="enabled">Enabled</label>
            <input
                id="enabled"
                type="checkbox"
                formControlName="enabled"
                (change)="detailForm.markAsDirty()"
            />
        </div>

        <div class="color-options-section">
            <h3>Color Options</h3>
            <div formArrayName="colorOptions">
                <div
                    *ngFor="let colorOption of colorOptionsFormArray.controls; let i = index"
                    [formGroupName]="i"
                    class="color-option-row"
                >
                    <div class="form-group">
                        <label [for]="'colorName' + i">Color Name</label>
                        <input
                            [id]="'colorName' + i"
                            type="text"
                            class="form-control"
                            formControlName="name"
                            placeholder="e.g., Red, Blue"
                            (input)="detailForm.markAsDirty()"
                        />
                    </div>

                    <div class="form-group">
                        <label [for]="'hexCode' + i">Hex Code</label>
                        <input
                            [id]="'hexCode' + i"
                            type="text"
                            class="form-control"
                            formControlName="hexCode"
                            placeholder="#000000"
                            pattern="^#[0-9A-Fa-f]{6}$"
                            (input)="detailForm.markAsDirty()"
                        />
                    </div>

                    <div class="form-group">
                        <label [for]="'priceModifier' + i">Price Modifier ($)</label>
                        <input
                            [id]="'priceModifier' + i"
                            type="number"
                            class="form-control"
                            step="0.01"
                            formControlName="priceModifier"
                            placeholder="0.00"
                            (input)="detailForm.markAsDirty()"
                        />
                    </div>

                    <button
                        type="button"
                        class="btn btn-sm btn-danger"
                        (click)="removeColorOption(i)"
                    >
                        Remove
                    </button>
                </div>

                <button
                    type="button"
                    class="btn btn-sm btn-secondary"
                    (click)="addColorOption()"
                >
                    Add Color Option
                </button>
            </div>
        </div>
    </form>
</div>
