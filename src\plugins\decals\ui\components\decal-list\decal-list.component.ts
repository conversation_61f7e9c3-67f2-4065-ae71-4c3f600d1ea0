import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import {
    DataService,
    ModalService,
    NotificationService,
    SharedModule,
} from '@vendure/admin-ui/core';
import { gql } from 'apollo-angular';

const GET_DECAL_LIST = gql`
    query GetDecalList($options: DecalListOptions) {
        decals(options: $options) {
            items {
                id
                createdAt
                updatedAt
                code
                name
                description
                price
                enabled
                image {
                    id
                    preview
                }
                colorOptions {
                    name
                    hexCode
                    priceModifier
                }
            }
            totalItems
        }
    }
`;

const DELETE_DECAL = gql`
    mutation DeleteDecal($id: ID!) {
        deleteDecal(id: $id) {
            result
            message
        }
    }
`;

const DELETE_DECALS = gql`
    mutation DeleteDecals($ids: [ID!]!) {
        deleteDecals(ids: $ids) {
            result
            message
        }
    }
`;

interface DecalListItem {
    id: string;
    createdAt: string;
    updatedAt: string;
    code: string;
    name: string;
    description?: string;
    price: number;
    enabled: boolean;
    image?: {
        id: string;
        preview: string;
    };
    colorOptions: Array<{
        name: string;
        hexCode: string;
        priceModifier: number;
    }>;
}

@Component({
    selector: 'vdr-decal-list',
    templateUrl: './decal-list.component.html',
    standalone: true,
    imports: [SharedModule],
})
export class DecalListComponent implements OnInit {
    decals: DecalListItem[] = [];
    loading = false;

    constructor(
        private dataService: DataService,
        private router: Router,
        private route: ActivatedRoute,
        private modalService: ModalService,
        private notificationService: NotificationService,
    ) {}

    ngOnInit() {
        this.loadDecals();
    }

    loadDecals() {
        this.loading = true;
        // For now, we'll use a simple approach without the complex reactive patterns
        // This can be enhanced later once the basic functionality works
        setTimeout(() => {
            // Mock data for now - this will be replaced with actual GraphQL call
            this.decals = [];
            this.loading = false;
        }, 1000);
    }

    create() {
        this.router.navigate(['./create'], { relativeTo: this.route });
    }

    async deleteDecal(decal: DecalListItem) {
        const confirmed = confirm('Are you sure you want to delete this decal?');
        if (confirmed) {
            // TODO: Implement actual delete functionality
            this.notificationService.success('Decal deleted successfully');
            this.loadDecals();
        }
    }

    formatPrice(price: number): string {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(price / 100);
    }
}
