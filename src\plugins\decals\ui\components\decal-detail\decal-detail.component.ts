import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { Form<PERSON>rray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import {
    NotificationService,
    SharedModule,
} from '@vendure/admin-ui/core';
import { gql } from 'apollo-angular';

export const GET_DECAL = gql`
    query GetDecal($id: ID!) {
        decal(id: $id) {
            id
            createdAt
            updatedAt
            code
            name
            description
            price
            enabled
            image {
                id
                name
                preview
                source
            }
            colorOptions {
                name
                hexCode
                priceModifier
            }
        }
    }
`;

export const CREATE_DECAL = gql`
    mutation CreateDecal($input: CreateDecalInput!) {
        createDecal(input: $input) {
            id
            code
            name
            description
            price
            enabled
            image {
                id
                preview
            }
            colorOptions {
                name
                hexCode
                priceModifier
            }
        }
    }
`;

export const UPDATE_DECAL = gql`
    mutation UpdateDecal($input: UpdateDecalInput!) {
        updateDecal(input: $input) {
            id
            code
            name
            description
            price
            enabled
            image {
                id
                preview
            }
            colorOptions {
                name
                hexCode
                priceModifier
            }
        }
    }
`;

@Component({
    selector: 'vdr-decal-detail',
    templateUrl: './decal-detail.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [SharedModule],
})
export class DecalDetailComponent implements OnInit {
    detailForm: FormGroup;
    isNew = true;
    decalId: string | null = null;

    constructor(
        private formBuilder: FormBuilder,
        private notificationService: NotificationService,
        private router: Router,
        private route: ActivatedRoute,
    ) {
        this.detailForm = this.formBuilder.group({
            code: ['', Validators.required],
            name: ['', Validators.required],
            description: [''],
            price: [0, [Validators.required, Validators.min(0)]],
            enabled: [true],
            imageId: [null],
            colorOptions: this.formBuilder.array([]),
        });
    }

    ngOnInit() {
        this.route.params.subscribe(params => {
            if (params['id']) {
                this.isNew = false;
                this.decalId = params['id'];
                this.loadDecal(params['id']);
            }
        });
    }

    get colorOptionsFormArray(): FormArray {
        return this.detailForm.get('colorOptions') as FormArray;
    }

    loadDecal(id: string) {
        // TODO: Implement actual loading from GraphQL
        // For now, just set up an empty form
        console.log('Loading decal with ID:', id);
    }

    create(): void {
        if (!this.detailForm.valid) {
            this.markFormGroupTouched(this.detailForm);
            return;
        }

        const formValue = this.detailForm.value;
        console.log('Creating decal with data:', formValue);

        // TODO: Implement actual create functionality
        this.notificationService.success('Decal created successfully');
        this.router.navigate(['../'], { relativeTo: this.route });
    }

    save(): void {
        if (!this.detailForm.valid) {
            this.markFormGroupTouched(this.detailForm);
            return;
        }

        // TODO: Implement actual save functionality
        this.notificationService.success('Decal updated successfully');
        this.detailForm.markAsPristine();
    }

    addColorOption(option?: any): void {
        const colorOptionGroup = this.formBuilder.group({
            name: [option?.name || '', Validators.required],
            hexCode: [option?.hexCode || '#000000', [Validators.required, Validators.pattern(/^#[0-9A-Fa-f]{6}$/)]],
            priceModifier: [option?.priceModifier ? option.priceModifier / 100 : 0, Validators.required],
        });
        this.colorOptionsFormArray.push(colorOptionGroup);
    }

    removeColorOption(index: number): void {
        this.colorOptionsFormArray.removeAt(index);
    }

    private markFormGroupTouched(formGroup: FormGroup): void {
        Object.keys(formGroup.controls).forEach(key => {
            const control = formGroup.get(key);
            control?.markAsTouched();

            if (control instanceof FormGroup) {
                this.markFormGroupTouched(control);
            }
        });
    }
}
